#!/usr/bin/env python3
"""
Test script to verify the enhanced crosshair system with 8-directional lines.
"""

import sys
import math
from pathlib import Path
from typing import List

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from PyQt6.QtWidgets import QApplication
from minimap_viewer import MinimapGraphicsView

def test_tibia_directional_angles():
    """Test the Tibia directional angle calculations."""
    print("Testing Tibia directional angle calculations...")
    
    # Create a test instance
    view = MinimapGraphicsView()
    
    # Get the angles
    angles = view.get_tibia_directional_angles()
    
    print(f"Number of angles: {len(angles)}")
    print("Directional angles:")
    directions = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]
    
    for i, (direction, angle) in enumerate(zip(directions, angles)):
        print(f"  {direction}: {angle}°")
    
    # Verify we have 8 angles
    assert len(angles) == 8, f"Expected 8 angles, got {len(angles)}"
    
    # Verify the angles are correct for the 8 cardinal and intercardinal directions
    expected_angles = [0.0, 45.0, 90.0, 135.0, 180.0, 225.0, 270.0, 315.0]
    for i, (expected, actual) in enumerate(zip(expected_angles, angles)):
        assert abs(expected - actual) < 0.1, f"Direction {directions[i]}: expected {expected}°, got {actual}°"
    
    print("✓ All directional angles are correct!")

def test_diagonal_line_length_calculation():
    """Test the diagonal line length calculation."""
    print("\nTesting diagonal line length calculation...")
    
    view = MinimapGraphicsView()
    
    # Test with different zoom factors
    zoom_factors = [0.1, 0.5, 1.0, 2.0, 5.0]
    
    for zoom in zoom_factors:
        view.zoom_factor = zoom
        length = view.calculate_diagonal_line_length()
        print(f"  Zoom {zoom}x: Line length = {length:.1f}")
        
        # Verify length is reasonable
        assert 20.0 <= length <= 1000.0, f"Line length {length} is outside reasonable bounds"
    
    print("✓ Diagonal line length calculations are working!")

def test_crosshair_storage():
    """Test the crosshair diagonal line storage."""
    print("\nTesting crosshair diagonal line storage...")
    
    view = MinimapGraphicsView()
    
    # Verify we have 8 diagonal line slots
    assert len(view.crosshair_diagonals) == 8, f"Expected 8 diagonal slots, got {len(view.crosshair_diagonals)}"
    
    # Verify all slots are initially None
    for i, line in enumerate(view.crosshair_diagonals):
        assert line is None, f"Diagonal line {i} should be None initially"
    
    print("✓ Crosshair diagonal line storage is properly initialized!")

def main():
    """Run all tests."""
    print("Testing Enhanced Crosshair System with 8-Directional Lines")
    print("=" * 60)

    # Create QApplication for Qt widgets
    app = QApplication(sys.argv)

    try:
        test_tibia_directional_angles()
        test_diagonal_line_length_calculation()
        test_crosshair_storage()
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed! The enhanced crosshair system is working correctly.")
        print("\nFeatures implemented:")
        print("✓ 8-directional lines (N, NE, E, SE, S, SW, W, NW)")
        print("✓ Tibia-accurate directional angles (45° intervals)")
        print("✓ Dynamic line length based on zoom level")
        print("✓ Proper storage and management of diagonal lines")
        print("✓ Integration with existing crosshair system")
        
        print("\nTo test visually:")
        print("1. Run: python main.py")
        print("2. Right-click on the minimap to place crosshairs")
        print("3. You should see 8 diagonal lines extending from the crosshair center")
        print("4. Zoom in/out to see the lines adjust their appearance")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
